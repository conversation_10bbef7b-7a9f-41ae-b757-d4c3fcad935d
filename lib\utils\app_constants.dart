import 'package:flutter/material.dart';

class AppConstants {
  // Informações da aplicação
  static const String appName = 'Flutter Básico';
  static const String appVersion = '1.0.0';

  // Configurações de UI
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  static const double defaultBorderRadius = 8.0;
  static const double smallBorderRadius = 4.0;
  static const double largeBorderRadius = 16.0;

  // Configurações de texto
  static const int maxContactNameLength = 100;
  static const int minContactNameLength = 2;

  // Mensagens de erro
  static const String emptyNameError = 'Nome do contato não pode estar vazio';
  static const String shortNameError = 'Nome deve ter pelo menos 2 caracteres';
  static const String longNameError =
      'Nome não pode ter mais de 100 caracteres';
  static const String duplicateNameError = 'Já existe um contato com este nome';
  static const String contactNotFoundError = 'Contato não encontrado';
  static const String genericError = 'Ocorreu um erro inesperado';

  // Mensagens de sucesso
  static const String contactCreatedSuccess = 'Contato adicionado com sucesso!';
  static const String contactUpdatedSuccess = 'Contato atualizado com sucesso!';
  static const String contactDeletedSuccess = 'Contato removido com sucesso!';

  // Configurações do Firestore
  static const String contactsCollection = 'contatos';

  // Configurações de animação
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // Cores personalizadas
  static const Color primaryColor = Color(0xFF9C3FE4);
  static const Color successColor = Colors.green;
  static const Color errorColor = Colors.red;
  static const Color warningColor = Colors.orange;
  static const Color infoColor = Colors.blue;

  // Configurações de layout
  static const double maxContentWidth = 600.0;
  static const double minButtonHeight = 48.0;

  // Configurações de ícones
  static const double defaultIconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double largeIconSize = 32.0;

  // Configurações de texto
  static const TextStyle titleTextStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle subtitleTextStyle = TextStyle(
    fontSize: 14,
    color: Colors.grey,
  );

  static const TextStyle bodyTextStyle = TextStyle(fontSize: 16);

  // Configurações de formulário
  static const String contactNameHint = 'Ex: João Silva';
  static const String contactNameLabel = 'Nome do contato';
  static const String addContactButtonText = 'Adicionar Contato';
  static const String updateContactButtonText = 'Atualizar Contato';
  static const String deleteContactButtonText = 'Remover Contato';

  // Configurações de lista vazia
  static const String emptyContactsTitle = 'Nenhum contato encontrado';
  static const String emptyContactsSubtitle =
      'Adicione um contato usando o formulário acima';

  // Configurações de loading
  static const String loadingMessage = 'Carregando...';
  static const String refreshingMessage = 'Atualizando...';

  // Configurações de confirmação
  static const String deleteConfirmationTitle = 'Confirmar exclusão';
  static const String deleteConfirmationMessage =
      'Tem certeza que deseja remover este contato?';
  static const String confirmButtonText = 'Confirmar';
  static const String cancelButtonText = 'Cancelar';
}
