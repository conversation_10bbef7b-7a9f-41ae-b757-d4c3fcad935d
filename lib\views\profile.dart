import 'package:flutter/material.dart';
import '../controllers/user_controller.dart';
import '../models/user_model.dart';
import '../repositories/user_repository.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final UserController _userController = UserController(UserRepository());
  AppUser? user;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUser();
  }

  Future<void> _loadUser() async {
    // Aqui você pode buscar o usuário logado, por enquanto busca o primeiro usuário da lista
    final users = await _userController.getAllUsers();
    setState(() {
      user = users.isNotEmpty ? users.first : null;
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Perfil do Usuário'),
        backgroundColor: const Color(0xFF9C3FE4),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : user == null
              ? const Center(child: Text('Nenhum usuário encontrado.'))
              : Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const CircleAvatar(
                        radius: 50,
                        backgroundColor: Color(0xFFD1C4E9),
                        child:
                            Icon(Icons.person, size: 50, color: Colors.white),
                      ),
                      const SizedBox(height: 10),
                      _buildEditableField(
                          TextEditingController(text: user!.name), 'Nome'),
                      _buildEditableField(
                          TextEditingController(text: user!.email), 'E-mail'),
                      _buildEditableField(
                          TextEditingController(text: user!.role), 'Cargo'),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildStatField(
                              TextEditingController(
                                  text: user!.createdAt.toString()),
                              'Criado em'),
                          _buildStatField(
                              TextEditingController(
                                  text: user!.updatedAt.toString()),
                              'Atualizado em'),
                        ],
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildEditableField(TextEditingController controller, String label) {
    return TextField(
      controller: controller,
      textAlign: TextAlign.center,
      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
      decoration: InputDecoration(
        border: InputBorder.none,
        hintText: label,
      ),
    );
  }

  Widget _buildStatField(TextEditingController controller, String label) {
    return Column(
      children: [
        SizedBox(
          width: 60,
          child: TextField(
            controller: controller,
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            decoration: const InputDecoration(border: InputBorder.none),
          ),
        ),
        Text(label, style: const TextStyle(fontSize: 14, color: Colors.grey)),
      ],
    );
  }
}

class _ActivityTile extends StatelessWidget {
  final String title;
  final String date;

  const _ActivityTile({required this.title, required this.date});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListTile(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(date),
        leading: const Icon(Icons.volunteer_activism, color: Color(0xFF9C3FE4)),
      ),
    );
  }
}
