import 'package:flutter/material.dart';
import '../controllers/auth_controller.dart';
import '../services/auth_service.dart';
import '../utils/app_constants.dart';
import '../utils/snackbar_helper.dart';

class UserProfileWidget extends StatelessWidget {
  final AuthController? authController;

  const UserProfileWidget({
    super.key,
    this.authController,
  });

  @override
  Widget build(BuildContext context) {
    final controller = authController ?? AuthController();
    
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        final user = controller.currentUser;
        
        if (user == null) {
          return const SizedBox.shrink();
        }

        return PopupMenuButton<String>(
          onSelected: (value) => _handleMenuSelection(context, value, controller),
          itemBuilder: (context) => [
            PopupMenuItem<String>(
              value: 'profile',
              child: Row(
                children: [
                  const Icon(Icons.person, size: 20),
                  const SizedBox(width: AppConstants.smallPadding),
                  const Text('Perfil'),
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'logout',
              child: Row(
                children: [
                  const Icon(Icons.logout, size: 20, color: AppConstants.errorColor),
                  const SizedBox(width: AppConstants.smallPadding),
                  const Text('Sair', style: TextStyle(color: AppConstants.errorColor)),
                ],
              ),
            ),
          ],
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildUserAvatar(user),
                const SizedBox(width: AppConstants.smallPadding),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _getDisplayName(user),
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (user.email.isNotEmpty)
                      Text(
                        user.email,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
                const SizedBox(width: AppConstants.smallPadding),
                const Icon(
                  Icons.arrow_drop_down,
                  size: 20,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserAvatar(AuthUser user) {
    if (user.photoURL != null && user.photoURL!.isNotEmpty) {
      return CircleAvatar(
        radius: 16,
        backgroundImage: NetworkImage(user.photoURL!),
        onBackgroundImageError: (exception, stackTrace) {
          // Em caso de erro ao carregar a imagem, mostra as iniciais
        },
        child: user.photoURL!.isEmpty ? Text(
          user.initials,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ) : null,
      );
    }

    return CircleAvatar(
      radius: 16,
      backgroundColor: AppConstants.primaryColor,
      child: Text(
        user.initials,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  String _getDisplayName(AuthUser user) {
    if (user.displayName.isNotEmpty) {
      return user.displayName;
    }
    
    if (user.email.isNotEmpty) {
      return user.email.split('@')[0];
    }
    
    return 'Usuário';
  }

  void _handleMenuSelection(BuildContext context, String value, AuthController controller) {
    switch (value) {
      case 'profile':
        _showProfileDialog(context, controller);
        break;
      case 'logout':
        _showLogoutDialog(context, controller);
        break;
    }
  }

  void _showProfileDialog(BuildContext context, AuthController controller) {
    final user = controller.currentUser;
    if (user == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Perfil do Usuário'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(child: _buildUserAvatar(user)),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildProfileInfo('Nome', user.displayName.isNotEmpty ? user.displayName : 'Não informado'),
            _buildProfileInfo('Email', user.email.isNotEmpty ? user.email : 'Não informado'),
            _buildProfileInfo('ID', user.uid),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fechar'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Saída'),
        content: const Text('Tem certeza que deseja sair da sua conta?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _handleLogout(context, controller);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sair'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context, AuthController controller) async {
    await controller.signOut();
    
    if (!context.mounted) return;

    if (controller.errorMessage != null) {
      SnackBarHelper.showError(
        context,
        controller.errorMessage!,
      );
    } else {
      SnackBarHelper.showSuccess(
        context,
        'Logout realizado com sucesso!',
      );
    }
  }
}
