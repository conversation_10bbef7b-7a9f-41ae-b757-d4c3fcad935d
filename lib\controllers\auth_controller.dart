import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';

class AuthController extends ChangeNotifier {
  final AuthService _authService = AuthService();

  bool _isLoading = false;
  String? _errorMessage;
  AuthUser? _currentUser;

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  AuthUser? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  AuthController() {
    _initializeAuthListener();
  }

  // Inicializar listener para mudanças de autenticação
  void _initializeAuthListener() {
    _authService.authStateChanges.listen((User? user) {
      if (user != null) {
        _currentUser = _authService.getUserInfo();
      } else {
        _currentUser = null;
      }
      notifyListeners();
    });

    // Definir usuário atual se já estiver logado
    _currentUser = _authService.getUserInfo();
  }

  // Cadastrar usuário
  Future<bool> signUp({
    required String name,
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.signUpWithEmailAndPassword(
        name: name,
        email: email,
        password: password,
      );

      _currentUser = _authService.getUserInfo();
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Login com email e senha
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _currentUser = _authService.getUserInfo();
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.signOut();
      _currentUser = null;
    } catch (e) {
      _setError(_getErrorMessage(e));
    } finally {
      _setLoading(false);
    }
  }

  // Resetar senha
  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.resetPassword(email);
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Deletar conta
  Future<bool> deleteAccount() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.deleteAccount();
      _currentUser = null;
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Métodos privados para gerenciar estado
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Obter mensagem de erro amigável
  String _getErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'weak-password':
          return 'A senha é muito fraca. Use pelo menos 6 caracteres.';
        case 'email-already-in-use':
          return 'Este email já está sendo usado por outra conta.';
        case 'invalid-email':
          return 'Email inválido.';
        case 'user-not-found':
          return 'Usuário não encontrado.';
        case 'wrong-password':
          return 'Senha incorreta.';
        case 'user-disabled':
          return 'Esta conta foi desabilitada.';
        case 'too-many-requests':
          return 'Muitas tentativas. Tente novamente mais tarde.';
        case 'network-request-failed':
          return 'Erro de conexão. Verifique sua internet.';
        case 'invalid-credential':
          return 'Credenciais inválidas.';
        default:
          return 'Erro de autenticação: ${error.message}';
      }
    }

    return error.toString();
  }

  // Limpar mensagem de erro
  void clearError() {
    _clearError();
  }

  // Validar email
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Validar senha
  bool isValidPassword(String password) {
    return password.length >= 6;
  }

  // Validar nome
  bool isValidName(String name) {
    return name.trim().length >= 2;
  }
}
