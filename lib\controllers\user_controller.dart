﻿import '../models/user_model.dart';
import '../repositories/user_repository.dart';

class UserController {
  final UserRepository _repository;

  UserController(this._repository);

  // Create new user
  Future<bool> createUser(AppUser user) async {
    try {
      await _repository.create(user);
      return true;
    } catch (e) {
      print('Error creating user: $e');
      return false;
    }
  }

  // Get all users
  Future<List<AppUser>> getAllUsers() async {
    try {
      return await _repository.getAll();
    } catch (e) {
      print('Error getting users: $e');
      return [];
    }
  }

  // Update existing user
  Future<bool> updateUser(AppUser user) async {
    try {
      await _repository.update(user);
      return true;
    } catch (e) {
      print('Error updating user: $e');
      return false;
    }
  }

  // Delete user
  Future<bool> deleteUser(String userId) async {
    try {
      await _repository.delete(userId);
      return true;
    } catch (e) {
      print('Error deleting user: $e');
      return false;
    }
  }

  // Get user by ID
  Future<AppUser?> getUserById(String userId) async {
    try {
      return await _repository.getById(userId);
    } catch (e) {
      print('Error getting user by ID: $e');
      return null;
    }
  }
}
