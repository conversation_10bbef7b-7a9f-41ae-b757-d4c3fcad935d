import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../config/google_config.dart';
import '../models/user_model.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  late final GoogleSignIn _googleSignIn;
  bool _isInitialized = false;

  // Stream para observar mudanças no estado de autenticação
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Stream para ouvir mudanças de autenticação
  Stream<User?> get userChanges => _auth.authStateChanges();

  // Usuário atual
  User? get currentUser => _auth.currentUser;

  // Verificar se o usuário está logado
  bool get isLoggedIn => currentUser != null;

  // Salvar/atualizar dados do usuário no Firestore
  Future<void> saveUserData(AppUser user) async {
    await _firestore.collection('users').doc(user.uid).set({
      'uid': user.uid,
      'name': user.name,
      'email': user.email,
      'createdAt': user.createdAt.toIso8601String() ?? '',
      'updatedAt': user.updatedAt.toIso8601String(),
    }, SetOptions(merge: true));
  }

  // Login com email e senha
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      throw AuthException('Erro ao fazer login: $e');
    }
  }

  // Login com e-mail e senha
  Future<UserCredential> signUpWithEmailAndPassword({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      // Criar usuário no Firebase Auth
      final UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password);

      // Atualizar o displayName do usuário
      await userCredential.user?.updateDisplayName(name);

      // Salvar dados adicionais do usuário no Firestore
      if (userCredential.user != null) {
        final appUser = AppUser(
          uid: userCredential.user!.uid,
          name: userCredential.user!.displayName ?? 'Sem Nome',
          email: userCredential.user!.email ?? 'Sem E-mail',
        );

        saveUserData(appUser);
      }

      return userCredential;
    } catch (e) {
      throw AuthException('Erro ao cadastrar usuário: $e');
    }
  }

  // Inicializar Google Sign In (deve ser chamado uma vez)
  Future<void> _initializeGoogleSignIn() async {
    if (_isInitialized) return;

    print('Inicializando Google Sign In...');

    if (kIsWeb) {
      // Para web, NÃO usar clientId - ele deve vir da meta tag
      _googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'profile',
          'openid'
        ], // openid é necessário para idToken
        // Remover clientId para web - usar apenas meta tag
      );
      print('Google Sign In inicializado para WEB (usando meta tag)');
    } else {
      // Para mobile, usar clientId
      _googleSignIn = GoogleSignIn(
        clientId: GoogleConfig.webClientId,
        scopes: [
          'email',
          'profile',
          'openid'
        ], // openid é necessário para idToken
      );
      print('Google Sign In inicializado para MOBILE');
    }

    _isInitialized = true;
  }

  Future<UserCredential> signInWithGoogle() async {
    try {
      if (!kIsWeb) {
        //futuramente criar metodo de conexão para android
        //turn signInWithGoogle); // Para mobile, usar método normal
      }

      // Cria uma instância do provedor de autenticação do Google.
      GoogleAuthProvider googleProvider = GoogleAuthProvider();

      // Você pode adicionar escopos para solicitar permissões adicionais.
      // Exemplo: para ler os contatos do usuário.
      googleProvider.addScope(
        'https://www.googleapis.com/auth/contacts.readonly',
      );
      googleProvider.setCustomParameters({'login_hint': '<EMAIL>'});

      // Faz o login com um Pop-up. O Firebase gerencia o fluxo para você.
      final UserCredential userCredential = await _auth.signInWithPopup(
        googleProvider,
      );

      // Salvar dados adicionais do usuário no Firestore
      if (userCredential.user != null) {
        final appUser = AppUser(
          uid: userCredential.user!.uid ?? '',
          name: userCredential.user!.displayName ?? 'Sem Nome',
          email: userCredential.user!.email ?? 'Sem E-mail',
        );
        saveUserData(appUser);
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      // Trata erros específicos do Firebase Auth
      throw AuthException('Erro ao fazer login com Google: ${e.message}');
    } catch (error) {
      // Trata outros possíveis erros
      throw AuthException('Erro ao fazer login com Google: $error');
    }
  }

  // Logout
  Future<void> signOut() async {
    try {
      if (_isInitialized) {
        await _googleSignIn.disconnect();
      }
      await _auth.signOut();
    } catch (e) {
      print('Erro no logout: $e');
    }
  }

  AuthUser? getUserInfo() {
    final user = currentUser;
    if (user == null) return null;

    return AuthUser(
      uid: user.uid,
      email: user.email ?? '',
      displayName: user.displayName ?? '',
      photoURL: user.photoURL,
    );
  }

  // Deletar conta
  Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Deletar dados do Firestore
        await _firestore.collection('users').doc(user.uid).delete();

        // Deletar conta do Firebase Auth
        await user.delete();
      }
    } catch (e) {
      throw AuthException('Erro ao deletar conta: $e');
    }
  }

  // Resetar senha
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw AuthException('Erro ao enviar email de recuperação: $e');
    }
  }
}

class AuthException implements Exception {
  final String message;

  AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}

class AuthUser {
  final String uid;
  final String email;
  final String displayName;
  final String? photoURL;

  AuthUser({
    required this.uid,
    required this.email,
    required this.displayName,
    this.photoURL,
  });

  // Obter iniciais do nome
  String get initials {
    if (displayName.isEmpty) return '?';

    List<String> words = displayName.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[words.length - 1][0]}'.toUpperCase();
    }
  }

  @override
  String toString() {
    return 'AuthUser{uid: $uid, email: $email, displayName: $displayName, photoURL: $photoURL}';
  }
}
