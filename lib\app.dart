import 'package:contask/views/login_page.dart';
import 'package:flutter/material.dart';
import 'core/routes.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'views/areaLogada.dart';
import 'views/login_screen.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Meu App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: AuthGate(),
      routes: appRoutes,
    );
  }
}

class AuthGate extends StatelessWidget {
  const AuthGate({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
              body: Center(child: CircularProgressIndicator()));
        }
        if (snapshot.hasData) {
          // Usuário autenticado
          return const AreaLogada();
        } else {
          // Não autenticado
          return const LoginPage();
        }
      },
    );
  }
}
